import re

pattern = ["231", "123"]

stack = "121"


def valid(stack, pattern):
    for i in pattern:
        if re.match(f"^{stack}", i):
            return True
    else:
        # if len(stack) <= 1:
        #     return False
        maxlen = max([len(i) for i in pattern])
        for i in range(1, maxlen):
            if i >= len(stack):
                continue
            if stack[:i] + "1" != stack and valid(stack[:i] + "1", pattern) and valid(stack[i:], pattern):
                return True
        else:
            return False


if __name__ == "__main__":
    print(valid(stack, pattern))
